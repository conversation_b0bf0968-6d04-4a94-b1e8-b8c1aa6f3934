{"name": "e3-laenud", "private": true, "version": "0.0.0", "main": "build/main.js", "scripts": {"dev": "vite", "dev:host": "vite --host", "build": "vite build", "postbuild": "yarn electron:build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "electron:build": "tsc --project electron", "electron:package": "electron-builder --win", "preelectron:package": "yarn build", "preelectron:dev": "yarn electron:build", "electron:dev": "electron .", "assets:update": "tsx --tsconfig scripts/tsconfig.json scripts/update.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@floating-ui/react": "^0.27.9", "@platvorm/i18n": "^1.0.0", "@swedbank/components": "^0.1.24", "dotenv": "^16.3.1", "es-toolkit": "^1.39.8", "js-yaml": "^4.1.0", "mobx": "^6.13.7", "mobx-react-observer": "^1.1.0", "motion": "^12.15.0", "react": "^18.2.0", "react-dom": "^18.2.0", "serialport": "^13.0.0", "styled-components": "^6.1.15"}, "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/node": "^22.13.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/serve-static": "^1.15.7", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@vitejs/plugin-react": "^4.2.0", "cross-env": "^7.0.3", "electron": "^34.1.1", "electron-builder": "^25.1.8", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "prettier": "^3.5.0", "serve-static": "^2.1.0", "tsx": "^4.19.2", "typescript": "^5.2.2", "vite": "^6.3.4", "vite-plugin-svgr": "^4.3.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}