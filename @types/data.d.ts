interface GameType {
	id: string;
	slide: string;
	timeLimit: number;
}
interface Game {
	id: string;
	title: string;
	onCompletedTitle: string;
	type: string;
	onCompleted: (isValid: boolean) => void;
}

interface Card {
	id: string;
	name: string;
	rows: Record<string, string>;
	footer?: string;
	info?: string;
}

interface Slot {
	id: string;
	name?: string;
}

interface ConnectItem {
	id: string;
	name: string;
	groups: string[];
}

interface ConnectColumn {
	id: string;
	name: string;
	items: ConnectItem[];
}

interface SortGame extends Game {
	cards: Card[];
	slots: Slot[];
}

interface ConnectGame extends Game {
	columns: ConnectColumn[];
	groupingId: string;
}

interface TableItem {
	id: string;
	name: string;
	droppable: boolean;
	size?: 'large' | 'medium';
}

interface TableColumn {
	id: string;
	name: string;
	items: TableItem[];
	footer?: string;
}

interface TableGame extends Game {
	columns: TableColumn[];
}

interface GamesData {
	gameOrder: string[];
	games: Game[];
}
