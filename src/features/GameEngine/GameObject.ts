import { Renderer } from './Renderer.ts';

export abstract class GameObject {
	public children: GameObject[] = [];

	constructor(public name: string = "GameObject") {}

	addChild(child: GameObject) {
		this.children.push(child);
	}

	removeChild(child: GameObject) {
		this.children = this.children.filter(c => c !== child);
	}

	update(deltaTime: number): void {
		for (const child of this.children) {
			child.update(deltaTime);
		}
	}

	draw(renderer: Renderer): void {
		for (const child of this.children) {
			child.draw(renderer);
		}
	}
}