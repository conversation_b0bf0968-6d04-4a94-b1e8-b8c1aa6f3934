import { GameSystem } from './GameSystem';
import { Game } from './Game';
import { Renderer } from './Renderer';

export class RenderSystem<T extends { type: string }> implements GameSystem<T> {
	constructor(private renderer: Renderer) {}

	update = (_: number, game: Game<T>): void => {
		this.renderer.clear();

		for (const obj of game.getAllObjects()) {
			obj.draw(this.renderer);
		}

		this.renderer.flush();
	};
}
