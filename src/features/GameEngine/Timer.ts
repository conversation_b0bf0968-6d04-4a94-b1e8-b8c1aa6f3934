import { GameObject } from './GameObject.ts';

export class Timer extends GameObject {
	private startTime: number = 0;
	private endTime: number = 0;
	private running = false;

	start() {
		this.startTime = performance.now();
		this.running = true;
	}

	stop() {
		this.endTime = performance.now();
		this.running = false;
	}

	update(deltaTime: number) {
		if (!this.running) return;

	}

	get timeLeft() {
		return Math.max(0, this.endTime - performance.now());
	}
}