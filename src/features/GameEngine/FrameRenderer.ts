import { Renderer } from './Renderer.ts';
import { Display } from './Display.ts';

export class <PERSON>ameRenderer implements Renderer {
	public readonly width: number;
	public readonly height: number;
	private frame: number[][];

	constructor(protected display: Display) {
		this.width = display.width;
		this.height = display.height;
		this.frame = this.createEmptyFrame();
	}

	private createEmptyFrame(): number[][] {
		return Array.from({ length: this.height }, () => Array(this.width).fill(0));
	}

	setPixel(x: number, y: number, brightness: number): void {
		if (x >= 0 && x < this.width && y >= 0 && y < this.height) {
			this.frame[y][x] = brightness;
		}
	}

	clear(): void {
		this.frame = this.createEmptyFrame();
	}

	getFrame(): number[][] {
		return this.frame;
	}

	flush(): void {
		this.display.writeFrame(this.frame);
	}
}
