type EventType<T> = T extends { type: infer U } ? U : never;

type EventHandler<TUnion, T extends EventType<TUnion>> =
	(event: Extract<TUnion, { type: T }>) => void;

export class EventBus<TUnion extends { type: string }> {
	private listeners: {
		[K in EventType<TUnion>]?: Event<PERSON><PERSON>ler<TUnion, K>[];
	} = {};

	on<T extends EventType<TUnion>>(type: T, handler: EventHandler<TUnion, T>) {
		if (!this.listeners[type]) {
			this.listeners[type] = [];
		}
		this.listeners[type]!.push(handler);
	}

	emit<T extends TUnion>(event: T) {
		this.listeners[event.type]?.forEach(handler =>
			(handler as EventHandler<TUnion, typeof event.type>)(event)
		);
	}

	off<T extends EventType<TUnion>>(type: T, handler: EventHandler<TUnion, T>) {
		this.listeners[type] = this.listeners[type]?.filter(h => h !== handler);
	}

	clear() {
		this.listeners = {};
	}
}
