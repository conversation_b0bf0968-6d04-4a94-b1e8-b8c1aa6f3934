import { GameObject } from './GameObject.ts';
import { GameSystem } from './GameSystem.ts';
import { EventBus } from './EventBus.ts';

export class Game<TEvents extends { type: string }> {
	private lastTime: number = performance.now();
	private rootObjects: GameObject[] = [];
	private systems: GameSystem<TEvents>[] = [];
	private running: boolean = false;
	private hasStarted: boolean = false;
	private eventBus = new EventBus<TEvents>();

	get events() {
		return this.eventBus;
	}

	addObject(obj: GameObject) {
		this.rootObjects.push(obj);
	}

	removeObject(obj: GameObject) {
		this.rootObjects = this.rootObjects.filter(o => o !== obj);
	}

	addSystem(system: GameSystem<TEvents>) {
		this.systems.push(system);
	}

	getAllObjects(): GameObject[] {
		const collect = (obj: GameObject): GameObject[] => {
			return [obj, ...obj.children.flatMap(collect)];
		};
		return this.rootObjects.flatMap(collect);
	}

	start() {
		this.running = true;
		if (!this.hasStarted) {
			this.systems.forEach(s => s.start?.(this));
			this.hasStarted = true;
		}
		requestAnimationFrame(this.loop);
	}

	stop() {
		this.running = false;
	}

	private loop = (time: number) => {
		if (!this.running) return;

		const deltaTime = (time - this.lastTime) / 1000;
		this.lastTime = time;

		// Update all objects
		for (const obj of this.rootObjects) {
			obj.update(deltaTime);
		}

		// Run all systems
		for (const system of this.systems) {
			system.update?.(deltaTime, this);
		}

		requestAnimationFrame(this.loop);
	};
}