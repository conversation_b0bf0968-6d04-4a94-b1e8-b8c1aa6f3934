import { makeAutoObservable } from 'mobx';
import { clamp } from 'es-toolkit';
import { ControllerButton } from '@/features/GameEngine';

export class WallButton implements ControllerButton {
	protected _pressed = false;
	protected _brightness = 0;
	public readonly coords: [number, number];

	constructor(
		protected _id: string,
		coords: [number, number],
	) {
		this.coords = coords;
		makeAutoObservable(this);
	}

	get id() {
		return this._id;
	}

	get pressed() {
		return this._pressed;
	}

	set pressed(value: boolean) {
		this._pressed = value;
	}

	/*
	 * Normalized brightness value between 0 and 1.
	 * 0 = off
	 * 1 = on
	 */
	set brightness(value: number) {
		this._brightness = clamp(value, 0, 1);
	}

	get brightness() {
		return this._brightness;
	}
}
