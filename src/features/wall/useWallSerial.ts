import { useEffect } from 'react';
import { ElectronSerial } from '@/utils/ElectronSerial.ts';
import { Env } from '@/utils/env.ts';
import { useWallContext } from '@/contexts/WallContext.tsx';
import { autorun } from 'mobx';

function normalizedToByteString(values: number[]): string {
	const bytes = values.map((v) => {
		const byteValue = Math.round(v * 255);
		if (byteValue === 10) {
			return 11;
		}
		return byteValue;
	});
	return String.fromCharCode(...bytes);
}

export function useWallSerial() {
	const { mainWall } = useWallContext();

	useEffect(() => {
		const cleanups: Array<() => void> = [];

		(async () => {
			const ledSerialNumber = Env.get('APP_LED_SERIAL');
			const buttonsSerialNumber = Env.get('APP_BUTTON_SERIAL');

			// Setup LED serial connection
			if (ledSerialNumber) {
				const ledSerial = await ElectronSerial.getConnection(
					{ serialNumber: ledSerialNumber },
					230400
				);

				if (ledSerial && ledSerial.isValid()) {
					console.log('[useWallSerial] Connected to LED serial');
					ledSerial.onData(data => console.log('[LED]', data));

					const clean = autorun(() => {
						const buttonValues = mainWall.buttons.map((b) => b.brightness);
						ledSerial.writeLine(normalizedToByteString(buttonValues));
					}, { delay: 10 });

					cleanups.push(clean);
				} else {
					console.warn('[useWallSerial] Failed to connect to LED serial');
				}
			}

			// Setup buttons serial connection
			if (buttonsSerialNumber) {
				const buttonsSerial = await ElectronSerial.getConnection(
					{ serialNumber: buttonsSerialNumber },
					115200
				);

				if (buttonsSerial && buttonsSerial.isValid()) {
					console.log('[useWallSerial] Connected to buttons serial');
					buttonsSerial.onData((data) => {
						const [type, args] = data.split(':');
						if (type === 'B') {
							const [id, value] = args.split(',');
							const button = mainWall.buttons[Number(id)];
							button.pressed = Boolean(Number(value));
						}
					});
				} else {
					console.warn('[useWallSerial] Failed to connect to buttons serial');
				}
			}
		})();

		return () => {
			cleanups.forEach((c) => c());
		};
	}, [mainWall.buttons]);
}
