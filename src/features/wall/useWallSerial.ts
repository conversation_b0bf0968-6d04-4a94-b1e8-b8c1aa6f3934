import { useEffect } from 'react';
import { ElectronSerial } from '@/utils/ElectronSerial.ts';
import { Env } from '@/utils/env.ts';
import { useWallContext } from '@/contexts/WallContext.tsx';
import { autorun } from 'mobx';

function normalizedToByteString(values: number[]): string {
	const bytes = values.map((v) => {
		const byteValue = Math.round(v * 255);
		if (byteValue === 10) {
			return 11;
		}
		return byteValue;
	});
	return String.fromCharCode(...bytes);
}

export function useWallSerial() {
	const { mainWall } = useWallContext();

	useEffect(() => {

		const cleanups: Array<() => void> = [];

		(async () => {
			const ports = await ElectronSerial.listPorts();
			console.log('SerialPorts:', ports);
			const ledSerialNumber = Env.get('APP_LED_SERIAL');
			const buttonsSerialNumber = Env.get('APP_BUTTON_SERIAL');

			const ledPort = ports?.find((p) => p.serialNumber === ledSerialNumber);
			if (ledPort) {
				const ledSerial = new ElectronSerial(ledPort.path, 230400);

				if (!ledSerial) return;

				ledSerial.onData(data => console.log(data));

				const clean = autorun(() => {
					const buttonValues = mainWall.buttons.map((b) => b.brightness);
					ledSerial.writeLine(normalizedToByteString(buttonValues));
				}, { delay: 10 });

				cleanups.push(clean);
			}

			const buttonsPort = ports?.find((p) => p.serialNumber === buttonsSerialNumber);
			if (buttonsPort) {
				const buttonsSerial = new ElectronSerial(buttonsPort.path, 115200);

				buttonsSerial.onData((data) => {
					const [type, args] = data.split(':');
					if (type === 'B') {
						const [id, value] = args.split(',');
						const button = mainWall.buttons[Number(id)];
						button.pressed = Boolean(Number(value));
					}
				});
			}

			return () => {
				cleanups.forEach((c) => c());
			};
		})();
	}, [mainWall.buttons]);
}
