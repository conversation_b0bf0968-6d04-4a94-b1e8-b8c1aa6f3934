import { WallButton } from '@/features/wall/WallButton.ts';
import { <PERSON><PERSON>utton, Display, InputProvider } from '@/features/GameEngine';

export class WallState implements InputProvider, Display {
	protected _buttons: WallButton[] = [];
	protected _width: number;
	protected _height: number;

	constructor(width: number, height: number) {
		this._width = width;
		this._height = height;

		// Start from top left
		for (let y = 0; y < height; y++) {
			for (let x = 0; x < width; x++) {
				const id = y * width + x;
				this._buttons.push(new WallButton(id.toString(), [x, y]));
			}
		}
	}

	writeFrame(frame: number[][]): void {
		frame.forEach((row, y) => {
			row.forEach((value, x) => {
				const button = this.getButtonByCoordinates(x, y);
				button.brightness = value;
			});
		});
	}

	getButtons: () => ControllerButton[] = () => this.buttons;

	get buttons() {
		return this._buttons;
	}

	get width() {
		return this._width;
	}

	get height() {
		return this._height;
	}

	clear = () => {
		this._buttons.forEach((b) => {
			b.brightness = 0;
		});
	};

	setNewBrightness = (newBrightness: number[]) => {
		if (newBrightness.length !== this._buttons.length) {
			throw new Error('Invalid number of states');
		}

		this._buttons.forEach((b, i) => {
			b.brightness = newBrightness[i];
		});
	};

	getButtonByCoordinates = (x: number, y: number) => {
		const id = y * this._width + x;
		return this._buttons[id];
	};

	get pressedButtons() {
		return this._buttons.filter((b) => b.pressed);
	}

	get pressedCoordinates() {
		return this.pressedButtons.map((b) => b.coords);
	}
}
