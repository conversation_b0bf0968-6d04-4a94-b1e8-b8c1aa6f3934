import { WallState } from '@/features/wall/WallState.ts';

export class WallMessageHandler {
	constructor(protected wallState: WallState) {}

	handleMessage = (message: string) => {
		const [type, args] = message.split(':');
		if (type === 'B') {
			this.handleButtonMessage(args);
		}
	};

	handleButtonMessage = (args: string) => {
		const [id, value] = args.split(',');
		const button = this.wallState.buttons.find((b) => b.id === id);
		if (button) {
			button.pressed = Boolean(Number(value));
		}
	};
}
