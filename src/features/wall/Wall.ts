import { WallState } from '@/features/wall/WallState.ts';
import { autorun } from 'mobx';


export class WallStateMessenger {
	constructor(protected wallState: WallState) {
		autorun(
			() => {
				const buttonValues = this.wallState.buttons.map((b) => b.brightness);
				this.sendMessage(buttonValues);
			},
			{ delay: 50 },
		);
	}

	sendMessage(values: number[]) {
		console.log('raw values', values);
	}
}

export class WallStateSerial extends WallStateMessenger {
	constructor(wallState: WallState) {
		super(wallState);
	}

	sendMessage(message: number[]) {
		const uint8Array = new Uint8Array(convertToBytes(message));
		console.log('uint8Array', uint8Array);
	}
}

function convertToBytes(values: number[]) {
	return values.map(convertToByteValue);
}

function convertToByteValue(values: number) {
	let v = Math.round(values * 255);

	if (v === 10) { // 10 is newline and will break the protocol
		v = 11;
	}
	return v;
}
