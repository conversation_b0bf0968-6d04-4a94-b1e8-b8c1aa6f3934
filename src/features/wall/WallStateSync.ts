import { WallState } from '@/features/wall/WallState.ts';
import { autorun, toJS } from 'mobx';

export class WallStateSync {
	protected _wallState: WallState;
	protected _broadcastChannel: BroadcastChannel = new BroadcastChannel('wall');
	protected _isBroadcasting = true;

	constructor(wallState: WallState) {
		this._wallState = wallState;

		autorun(() => {
			if (!this._isBroadcasting) return;
			this._broadcastChannel.postMessage(toJS(this._wallState.buttons));
		});

		this._broadcastChannel.addEventListener('message', (e) => this.handleMessage(e));
	}

	handleMessage = (event: MessageEvent) => {
		this._wallState.buttons.forEach((button, i) => {
			if (button.brightness !== event.data[i]._value) {
				button.brightness = event.data[i]._value;
			}
			if (button.pressed !== event.data[i]._isPressed) {
				button.pressed = event.data[i]._isPressed;
			}
		});
	};

	stopBroadcasting() {
		this._isBroadcasting = false;
	}

	startBroadcasting() {
		this._isBroadcasting = true;
	}
}
