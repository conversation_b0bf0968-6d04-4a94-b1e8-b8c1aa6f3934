import { Frame<PERSON><PERSON><PERSON>, Game, RenderSystem, Display, InputProvider } from '@/features/GameEngine';
import { PhaseConfig, SpawnSystem } from './SpawnSystem.ts';
import { HitSystem } from './HitSystem.ts';
import { InputService } from './InputService.ts';
import { ScoreSystem } from './ScoreSystem.ts';
import { WhacEvents } from './events.ts';
import { InputSystem } from './InputSystem.ts';
import { SpawnService } from './SpawnService.ts';

// interface SystemContext {
// 	inputService: InputService;
// 	renderer: Renderer;
// }

// const phases: PhaseConfig[] = [
// 	{ startTime: 0, moles: 1 },
// 	{ startTime: 33, moles: 5 },
// 	{ startTime: 66, moles: 10 },
// ];

export function createWhacAMoleGame(playerCount: number, display: Display, inputProvider: InputProvider) {
	const game = new Game<WhacEvents>();

	const renderer = new FrameRenderer(display);
	const inputService = new InputService(inputProvider);

	const spawnService = new SpawnService({
		zoneCount: playerCount,
		width: renderer.width,
		height: renderer.height,
	});

	game.addSystem(new InputSystem(inputService));
	game.addSystem(new SpawnSystem(spawnService));
	game.addSystem(new HitSystem());
	game.addSystem(new ScoreSystem());
	game.addSystem(new RenderSystem(renderer));

	return game;
}
