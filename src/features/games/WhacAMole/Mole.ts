import { <PERSON>Object, Renderer, Vector2 } from '@/features/GameEngine';
import { randomInt } from 'es-toolkit';

type Range = [Vector2, Vector2];

export class <PERSON>le extends GameObject {
	public position: Vector2 = new Vector2(0, 0);
	protected zone: Range = [new Vector2(0, 0), new Vector2(0, 0)];
	protected isVisible = false;

	setZone(zone: Range) {
		this.zone = zone;
	}

	setVisible(visible: boolean) {
		this.isVisible = visible;
	}

	takeDamage() {
		this.isVisible = false;
		this.relocate();
		this.isVisible = true;
	}

	checkHit(x: number, y: number) {
		if (this.position.x === x && this.position.y === y) {
			this.takeDamage();
			return true;
		}
		return false;
	}

	relocate() {
		const [min, max] = this.zone;
		this.position = new Vector2(randomInt(min.x, max.x), randomInt(min.y, max.y));
	}

	draw(renderer: Renderer) {
		if (!this.isVisible) return;

		renderer.setPixel(this.position.x, this.position.y, 1);
	}
}
