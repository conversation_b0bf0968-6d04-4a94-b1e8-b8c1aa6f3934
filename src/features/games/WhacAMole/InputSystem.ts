import { isEqual } from 'es-toolkit';
import { Game, GameSystem } from '@/features/GameEngine';
import { WhacEvents } from '@/features/games/WhacAMole/events.ts';
import { InputService } from '@/features/games/WhacAMole/InputService.ts';

function isSame(coords: [number, number]) {
	return (otherCoords: [number, number]) => isEqual(coords, otherCoords);
}

export class InputSystem implements GameSystem<WhacEvents> {
	previouslyPressedCoordinates: [number, number][] = [];
	constructor(protected inputService: InputService) {}

	update(_deltaTime: number, game: Game<WhacEvents>) {
		const pressedCoordinates = this.inputService.getPressedCoordinates();

		const newPresses = pressedCoordinates.filter(
			(currentCoords) => !this.previouslyPressedCoordinates.some(isSame(currentCoords)),
		);

		const newReleases = this.previouslyPressedCoordinates.filter(
			(previousCoordinates) => !pressedCoordinates.some(isSame(previousCoordinates)),
		);

		const buttonPresses = pressedCoordinates.filter((newCoords) => !newPresses.some(isSame(newCoords)));

		this.previouslyPressedCoordinates = pressedCoordinates;

		newPresses.forEach((c) => {
			game.events.emit({ type: 'buttonPressBegin', payload: { coordinates: c } });
		});

		newReleases.forEach((c) => {
			game.events.emit({ type: 'buttonPressEnd', payload: { coordinates: c } });
		});

		buttonPresses.forEach((c) => {
			game.events.emit({ type: 'buttonPressed', payload: { coordinates: c } });
		});
	}
}