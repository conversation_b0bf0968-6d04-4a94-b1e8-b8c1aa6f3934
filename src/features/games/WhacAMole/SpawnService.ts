import { <PERSON><PERSON> } from '@/features/games/WhacAMole/Mole.ts';
import { Vector2 } from '@/features/GameEngine';

interface SpawnServiceOptions {
	zoneCount: number;
	width: number;
	height: number;
}

export class SpawnService {
	constructor(protected options: SpawnServiceOptions) {}

	createMoles(count: number) {
		return Array.from({ length: count }).map(() => this.createMole());
	}

	createMole() {
		const mole = new Mole();
		mole.setZone([new Vector2(0, 0), new Vector2(this.options.width, this.options.height)]);
		mole.relocate();
		mole.setVisible(true);
		return mole;
	}
}
