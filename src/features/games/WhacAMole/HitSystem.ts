import { Game, GameSystem } from '@/features/GameEngine';
import { <PERSON><PERSON> } from '@/features/games/WhacAMole/Mole.ts';
import { WhacEvents } from '@/features/games/WhacAMole/events.ts';

export class HitSystem implements GameSystem<WhacEvents> {
	start(game: Game<WhacEvents>) {
		game.events.on('buttonPressBegin', (event) => {
			game.getAllObjects().forEach((obj) => {
				if (!(obj instanceof Mole)) {
					return;
				}

				const [x, y] = event.payload.coordinates;
				if (obj.checkHit(x, y)) {
					obj.takeDamage();
					game.events.emit({ type: 'moleHit', payload: { mole: obj } });
				}
			});
		});
	}
}
