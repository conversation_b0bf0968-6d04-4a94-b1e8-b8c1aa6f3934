import { GameSystem, Game } from '@/features/GameEngine';
import { SpawnService } from './SpawnService.ts';
import { WhacEvents } from './events.ts';

export interface PhaseConfig {
	startTime: number;
	moles: number;
}

export class SpawnSystem implements GameSystem<WhacEvents> {
	constructor(protected spawnService: SpawnService) {}

	start(game: Game<WhacEvents>): void {
		const moles = this.spawnService.createMoles(4);

		moles.forEach((mole) => {
			game.addObject(mole);
		});
	}

	// override update(deltaTime: number, game: Game) {}
}
