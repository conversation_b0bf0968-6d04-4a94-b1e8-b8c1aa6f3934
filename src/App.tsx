import { LocalStyles } from './styles/LocalStyles.ts';
import { GlobalStyles, IntroSlides } from '@swedbank/components';
import { useEffect, useRef } from 'react';
import { useAppContext } from './contexts/AppContext.tsx';
import { useTimer } from './hooks/useTimer.ts';
import './styles/fonts/fonts.css';
import { GameView } from './components/GameView.tsx';
import { useSlides } from './hooks/useSlides.ts';
import { getMediaPath } from './utils/assets.ts';
import { WallDebug } from '@/components/Wall/WallDebug.tsx';
import { useWallContext } from '@/contexts/WallContext.tsx';
import { useWallSerial } from '@/features/wall/useWallSerial.ts';

function App() {
	const { globalConfig, startGame, gameStarted } = useAppContext();
	const introSlides = useSlides({ slides: globalConfig?.slides, onSlidesFinished: startGame });
	const { mainWall } = useWallContext();

	const { start: startIntroTimer, seconds: introTimeLeft } = useTimer(() => startGame());

	useWallSerial();

	useEffect(() => {
		if (!globalConfig) return;
		startIntroTimer(globalConfig.introTimeLimit);
	}, [globalConfig, startIntroTimer]);

	return (
		<>
			<GlobalStyles />
			<LocalStyles />

			<WallDebug wallState={mainWall} />

			{gameStarted && <GameView />}

			{!gameStarted && (
				<IntroSlides introSlides={introSlides} introTimeLeft={introTimeLeft} getMediaPath={getMediaPath} />
			)}
		</>
	);
}

export default App;
