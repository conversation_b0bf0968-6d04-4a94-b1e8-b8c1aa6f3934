import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
import { loadConfig } from '@/utils/config-loader.ts';
import { RootContextProvider } from './contexts/RootContext.tsx';
import { LocaleContextProvider } from '@platvorm/i18n';
import { getAssetPath } from '@/utils/assets.ts';
import { AppContextProvider } from './contexts/AppContext.tsx';

(async () => {
	const isApp = !!window.api?.electron();
	const config = await loadConfig({ isApp });

	console.log('CONFIG: ', config);

	ReactDOM.createRoot(document.getElementById('root')!).render(
		<React.StrictMode>
			<RootContextProvider config={config}>
				<LocaleContextProvider
					defaultLocale={config.i18n?.defaultLocale || 'et'}
					locales={config.i18n?.locales || ['et']}
					assetPath={getAssetPath('locales')}
					mdKeys={['name', 'text']}
				>
					<AppContextProvider>
						<App />
					</AppContextProvider>
				</LocaleContextProvider>
			</RootContextProvider>
		</React.StrictMode>,
	);
})();
