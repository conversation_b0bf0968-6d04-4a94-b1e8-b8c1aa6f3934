import { useData } from '@/hooks/useData';
import { useTimer } from '@/hooks/useTimer';
import { createContext, PropsWithChildren, useCallback, useContext, useEffect } from 'react';
import { useMemo, useState } from 'react';

interface AppContextProps {
	globalConfig: ConfigRecord | null;
	gameConfig: GameType | undefined;
	startGame: () => void;
	currentGame: Game | undefined;
	nextGame: () => void;
	gameStarted: boolean;
	showOutro: boolean;
	setShowOutro: (show: boolean) => void;
	restartGame: () => void;
	totalGameTimeLeft: number;
}

const AppContext = createContext<AppContextProps>({} as AppContextProps);

interface AppContextProviderProps {}

export const AppContextProvider = ({ children }: PropsWithChildren<AppContextProviderProps>) => {
	const globalConfig = useData<ConfigRecord>('config.yml');
	const data = useData<GamesData>('data.yml');
	const [currentGameIndex, setCurrentGameIndex] = useState(0);
	const { start: startGameTimer, stop: stopGameTimer, seconds: totalGameTimeLeft } = useTimer();
	const [gameStarted, setGameStarted] = useState(false);
	const [gameEnded, setGameEnded] = useState(true);
	const [showOutro, setShowOutro] = useState(false);

	const startGame = useCallback(() => {
		setGameStarted(true);
	}, []);

	const restartGame = useCallback(() => {
		window.location.reload();
	}, []);

	const nextGame = useCallback(() => {
		if (!globalConfig) return;

		if (currentGameIndex < globalConfig?.gameTypes.length - 1) {
			setCurrentGameIndex((prevIndex) => prevIndex + 1);
		} else {
			setCurrentGameIndex(0);
		}
	}, [globalConfig, currentGameIndex]);

	const gameConfig = useMemo(
		() => globalConfig?.gameTypes[currentGameIndex],
		[globalConfig?.gameTypes, currentGameIndex],
	);

	const currentGame = useMemo(() => data?.games.find((game) => game.id === gameConfig?.id), [data?.games, gameConfig]);

	useEffect(() => {
		if (gameStarted && gameConfig) {
			startGameTimer(gameConfig.timeLimit);
			setGameEnded(false);
			setShowOutro(false);
		}

		return () => {
			stopGameTimer();
		};
	}, [gameConfig, gameStarted, startGameTimer, stopGameTimer]);

	useEffect(() => {
		if (gameStarted && totalGameTimeLeft === 0 && !gameEnded) {
			setShowOutro(true);
		}
	}, [gameEnded, gameStarted, totalGameTimeLeft]);

	return (
		<AppContext.Provider
			value={{
				globalConfig,
				gameConfig,
				startGame,
				totalGameTimeLeft,
				gameStarted,
				showOutro,
				setShowOutro,
				restartGame,
				currentGame,
				nextGame,
			}}
		>
			{children}
		</AppContext.Provider>
	);
};

export const useAppContext = () => useContext(AppContext);
