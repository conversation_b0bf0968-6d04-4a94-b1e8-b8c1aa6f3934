import { createContext, FC, ReactNode, useContext } from 'react';

type ConfigRecord = Record<string, any>;

interface RootContextProps {
	config: ConfigRecord;
}

export const RootContext = createContext<RootContextProps>({} as RootContextProps);

interface RootContextProviderProps {
	children?: ReactNode;
	config: ConfigRecord;
}

export const RootContextProvider: FC<RootContextProviderProps> = ({ children, config }) => {
	return <RootContext.Provider value={{ config }} children={children} />;
};

export const useRootContext = () => useContext(RootContext);
