import {
	DndContext,
	Drag<PERSON><PERSON>lay,
	DragStartEvent,
	TouchSensor,
	useDroppable,
	useSensor,
	useSensors,
} from '@dnd-kit/core';
import { FC, useCallback, useEffect, useState } from 'react';
import styled from 'styled-components';
import { Card, MovableCard } from './MovableCard';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';
import { CardSlot } from './CardSlot';
import { createPortal } from 'react-dom';
import { TextButton } from '@swedbank/components';

const GameRows = styled.div`
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 64px;
`;

const CardContainer = styled.div`
	display: grid;
	grid-template-columns: repeat(5, 666px);
	grid-template-rows: 656px;
	gap: 64px;
	height: 656px;
`;

const CardSlotContainer = styled.div`
	display: flex;
	gap: 64px;
`;

const InitialSlot = styled.div`
	background-color: var(--white);
	opacity: 0.5;
	border-radius: 32px;
`;
const InitialSlots = styled.div`
	position: absolute;
	display: grid;
	grid-template-columns: repeat(5, 666px);
	grid-template-rows: 656px;
	gap: 64px;
	height: 656px;
`;
interface CardsProps {
	remainingCards: Card[];
	initialCards: Card[];
	canAnimate?: boolean;
}

const Cards: FC<CardsProps> = ({ remainingCards, initialCards, canAnimate }) => {
	const { setNodeRef } = useDroppable({
		id: 'card-container',
	});

	return (
		<>
			<InitialSlots>
				{initialCards.map((card) => (
					<InitialSlot key={card.id} />
				))}
			</InitialSlots>
			<CardContainer ref={setNodeRef}>
				{remainingCards.map((card) => (
					<MovableCard
						key={card.id}
						id={card.id}
						title={card.name}
						rows={card.rows}
						footer={card.footer}
						info={card.info}
						initialIndex={initialCards.findIndex((initialCard) => initialCard.id === card.id)}
						canAnimate={canAnimate}
					/>
				))}
			</CardContainer>
		</>
	);
};

interface MovableCardsProps {
	cards: Card[];
	slots: Slot[];
	onCompleted?: (isValid: boolean) => void;
}

export const MovableCards: FC<MovableCardsProps> = ({ cards, slots, onCompleted }) => {
	const [completed, setCompleted] = useState(false);
	const [canAnimate, setCanAnimate] = useState(false);
	const [activeCard, setActiveCard] = useState<Card | null>(null);
	const [validSlots, setValidSlots] = useState<Card[]>([]);
	const [checkedValidSlots, setCheckedValidSlots] = useState<Card[]>([]);
	const [remainingCards, setRemainingCards] = useState<Card[]>(cards);
	const sensors = useSensors(useSensor(TouchSensor, { activationConstraint: { distance: 10 } }));

	const handleDragStart = useCallback(
		(event: DragStartEvent) => {
			const { active } = event;
			const card = cards.find((c) => c.id === active.id);
			setActiveCard(card || null);
		},
		[cards],
	);

	const handleCardCorrect = useCallback((correctCard: Card) => {
		setValidSlots((prev) => {
			if (prev.some((c) => c.id === correctCard.id)) {
				return prev;
			}
			return [...prev, correctCard];
		});
	}, []);

	const handleCardIncorrect = useCallback((card: Card) => {
		setValidSlots((prev) => prev.filter((c) => c.id !== card.id));
	}, []);

	const resetIncorrectSlots = useCallback(() => {
		setCanAnimate(true);

		const canAnimateTimeout = setTimeout(() => {
			setCanAnimate(false);
		}, 500);

		return () => {
			clearTimeout(canAnimateTimeout);
			setCanAnimate(false);
		};
	}, []);

	const handleOrderCheck = useCallback(() => {
		if (completed) return;
		setCheckedValidSlots(validSlots);
		if (validSlots.length === slots.length) {
			const isValid = validSlots.every((card) => slots.some((slot) => slot.id === card.id));
			onCompleted?.(isValid);
			setCompleted(true);
		} else {
			resetIncorrectSlots();
			onCompleted?.(false);
			setCompleted(false);
		}
	}, [completed, validSlots, slots, onCompleted, resetIncorrectSlots]);

	useEffect(() => {
		if (canAnimate) {
			setRemainingCards(cards.filter((card) => !validSlots.some((c) => c.id === card.id)));
		}
	}, [canAnimate, cards, validSlots]);

	return (
		<DndContext
			sensors={sensors}
			modifiers={[restrictToWindowEdges]}
			onDragStart={handleDragStart}
			onDragEnd={() => setActiveCard(null)}
			onDragCancel={() => setActiveCard(null)}
		>
			<GameRows>
				<Cards remainingCards={remainingCards} initialCards={cards} canAnimate={canAnimate} />
				<CardSlotContainer>
					{slots.map((slot, index) => (
						<CardSlot
							id={slot.id}
							slotIndex={index}
							name={slot.name}
							key={slot.id}
							cards={cards}
							remainingCards={remainingCards}
							setRemainingCards={setRemainingCards}
							onCardCorrect={handleCardCorrect}
							onCardIncorrect={handleCardIncorrect}
							canAnimate={canAnimate}
							isValid={checkedValidSlots.some((c) => c.id === slot.id)}
						/>
					))}
				</CardSlotContainer>

				<TextButton onClick={handleOrderCheck} variant="primary" disabled={completed}>
					Kinnita järjekord
				</TextButton>
			</GameRows>
			{createPortal(
				<DragOverlay>
					{activeCard ? (
						<Card title={activeCard.name} rows={activeCard.rows} footer={activeCard.footer} info={activeCard.info} />
					) : null}
				</DragOverlay>,
				document.body,
			)}
		</DndContext>
	);
};
