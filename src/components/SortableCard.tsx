import styled from 'styled-components';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface CardProps {
	$variant?: 'default' | 'positive' | 'negative' | 'active';
}

const Card = styled.div<CardProps>`
	background-color: ${(p) => `var(--${p.$variant || 'white'})`};
	border-radius: 32px;
	padding: 48px;
`;

const Table = styled.div`
	display: flex;
	flex-direction: column;
`;

const TableRow = styled.div`
	display: flex;
	justify-content: space-between;
	align-items: baseline;
	padding: 24px 0;

	&:not(:last-child) {
		border-bottom: 2px solid var(--border-color);
	}
`;
const TableKey = styled.div`
	&::first-letter {
		text-transform: uppercase;
	}
`;
const TableValue = styled.div`
	font-family: 'Swedbank Headline', sans-serif;
	font-weight: 900;
	font-size: 60px;
	color: var(--swedbank-orange-text);
`;

const Footer = styled.div`
	font-size: 24px;
	margin-top: 16px;
`;

interface SortableCardProps {
	id: string;
	title?: string;
	rows?: Record<string, string>;
	footer?: string;
	variant?: 'positive' | 'negative';
}

export const SortableCard = ({ id, title, rows, footer, variant }: SortableCardProps) => {
	const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id });

	const style: React.CSSProperties = {
		transform: CSS.Transform.toString(transform),
		position: isDragging ? 'relative' : 'inherit',
		zIndex: isDragging ? 1000 : 0,
		transition,
	};

	return (
		<Card ref={setNodeRef} style={style} {...attributes} {...listeners} $variant={isDragging ? 'active' : variant}>
			{title && <h3>{title}</h3>}

			{rows && (
				<Table>
					{Object.entries(rows).map(([key, value]) => (
						<TableRow key={key}>
							<TableKey>{key}</TableKey>
							<TableValue>{value}</TableValue>
						</TableRow>
					))}
				</Table>
			)}

			{footer && <Footer>{footer}</Footer>}
		</Card>
	);
};
