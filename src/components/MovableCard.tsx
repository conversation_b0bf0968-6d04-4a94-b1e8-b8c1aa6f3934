import styled from 'styled-components';
import { CSS } from '@dnd-kit/utilities';
import { useDraggable } from '@dnd-kit/core';
import { FC, useMemo } from 'react';
import { InfoPopover } from './InfoPopover';
import { LayoutProps, motion } from 'motion/react';

interface StyledCardProps {
	$variant: 'default' | 'positive' | 'negative' | 'active';
}

const StyledCard = styled.div<StyledCardProps>`
	position: relative;
	background-color: ${(p) => `var(--${p.$variant === 'active' ? 'active' : 'white'})`};
	box-shadow: ${(p) =>
		p.$variant === 'active' || p.$variant === 'default'
			? 'none'
			: `inset 0px 0px 0px 8px var(--${p.$variant}-transparent)`};
	border-radius: 32px;
	padding: 56px 48px;
	width: 666px;
	height: 656px;
	grid-row: 1;

	h3 {
		font-size: 64px;
	}
`;

const Table = styled.div`
	display: flex;
	flex-direction: column;
`;

const TableRow = styled.div`
	display: flex;
	justify-content: space-between;
	align-items: baseline;
	padding: 24px 0;

	&:not(:last-child) {
		border-bottom: 2px solid var(--border-color);
	}
`;
const TableKey = styled.div`
	&::first-letter {
		text-transform: uppercase;
	}
`;
const TableValue = styled.div`
	font-family: 'Swedbank Headline', sans-serif;
	font-weight: 900;
	font-size: 60px;
	color: var(--swedbank-orange-text);
`;

const Footer = styled.div`
	font-size: 24px;
	margin-top: 16px;
`;

interface MovableCardProps {
	id: string;
	title?: string;
	rows?: Record<string, string>;
	footer?: string;
	info?: string;
	variant?: 'positive' | 'negative';
	initialIndex?: number;
	canAnimate?: boolean;
	draggable?: boolean;
}

export const MovableCard: FC<MovableCardProps> = ({
	id,
	title,
	rows,
	footer,
	info,
	variant,
	initialIndex,
	canAnimate,
	draggable = true,
}) => {
	const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({ id, disabled: !draggable });

	const style: React.CSSProperties = {
		transform: CSS.Translate.toString(transform),
		position: draggable && (isDragging || canAnimate) ? 'relative' : 'inherit',
		zIndex: draggable && (isDragging || canAnimate) ? 3 : 0,
		gridColumn: initialIndex ? initialIndex + 1 : undefined,
		gridRow: 1,
		width: 666,
	};

	const layoutAnimationProps: LayoutProps = useMemo(
		() => (!canAnimate ? {} : { layout: 'position', layoutId: `card-${id}`, transition: { duration: 0.3 } }),
		[canAnimate, id],
	);

	return (
		<motion.div
			ref={setNodeRef}
			style={style}
			{...attributes}
			{...listeners}
			{...layoutAnimationProps}
			key={`${layoutAnimationProps.layoutId}`}
		>
			<Card variant={isDragging ? 'active' : variant} title={title} rows={rows} footer={footer} info={info} />
		</motion.div>
	);
};

interface CardProps {
	variant?: 'default' | 'positive' | 'negative' | 'active';
	title?: string;
	rows?: Record<string, string>;
	footer?: string;
	info?: string;
}

export const Card: FC<CardProps> = ({ variant, title, rows, footer, info }) => {
	return (
		<StyledCard $variant={variant || 'default'}>
			{title && <h3>{title}</h3>}
			{info && <InfoPopover>{info}</InfoPopover>}
			{rows && (
				<Table>
					{Object.entries(rows).map(([key, value]) => (
						<TableRow key={key}>
							<TableKey>{key}</TableKey>
							<TableValue>{value}</TableValue>
						</TableRow>
					))}
				</Table>
			)}

			{footer && <Footer>{footer}</Footer>}
		</StyledCard>
	);
};
