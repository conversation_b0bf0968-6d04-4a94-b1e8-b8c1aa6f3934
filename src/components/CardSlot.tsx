import { useDroppable, Active, useDndMonitor } from '@dnd-kit/core';
import { FC, useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import { MovableCard } from './MovableCard';

const CardSlotWrapper = styled.div`
	width: 666px;
	height: 656px;
`;

const CardSlotBottom = styled.div`
	padding: 48px;
	color: var(--white);
	background-color: var(--text);
	opacity: 0.2;
	position: absolute;
	z-index: -1;
	border-radius: 32px;
	width: 666px;
	height: 656px;

	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	gap: 16px;

	font-family: 'Swedbank Headline', sans-serif;
	font-weight: 900;
	font-size: 68px;
`;

interface CardSlotProps {
	id: string;
	slotIndex: number;
	name?: string;
	cards: Card[];
	remainingCards: Card[];
	setRemainingCards: React.Dispatch<React.SetStateAction<Card[]>>;
	onCardCorrect: (card: Card) => void;
	onCardIncorrect: (card: Card) => void;
	canAnimate?: boolean;
	isValid?: boolean;
}

export const CardSlot: FC<CardSlotProps> = ({
	id,
	slotIndex,
	name,
	cards,
	remainingCards,
	setRemainingCards,
	onCardCorrect,
	onCardIncorrect,
	canAnimate,
	isValid,
}) => {
	const [card, setCard] = useState<Card | null>(null);
	const { setNodeRef } = useDroppable({
		id,
	});

	useEffect(() => {
		if (card && remainingCards.some((c) => c.id === card.id)) {
			setCard(null);
		}
	}, [card, remainingCards]);

	useEffect(() => {
		if (card && card.id === id) {
			onCardCorrect(card);
		} else if (card) {
			onCardIncorrect(card);
		}
	}, [card, id, onCardCorrect, onCardIncorrect]);

	const handleDropOutside = useCallback(
		(active: Active) => {
			if (card?.id === active.id) {
				setCard(null);
				onCardIncorrect(card);
				setRemainingCards((prev) => [...prev, card]);
			}
		},
		[card, onCardIncorrect, setRemainingCards],
	);

	const handleDropInside = useCallback(
		(active: Active) => {
			if (isValid) {
				return;
			}
			if (remainingCards.some((c) => c.id === active.id)) {
				setRemainingCards((prev) => prev.filter((c) => c.id !== active.id));
			}
			if (card && card.id !== active.id) {
				onCardIncorrect(card);
				setRemainingCards((prev) => [...prev, card]);
			}
			setCard(cards.find((c) => c.id === active.id) || null);
		},
		[card, cards, isValid, onCardIncorrect, remainingCards, setRemainingCards],
	);

	useDndMonitor({
		onDragEnd: (event) => {
			const { active, over } = event;
			if (!over || over.id === 'card-container') {
				handleDropOutside(active);
			} else {
				if (over.id === id) {
					handleDropInside(active);
					if (active.id === card?.id) {
						handleDropOutside(active);
					}
				} else if (active.id === card?.id) {
					setCard(null);
				}
			}
		},
	});

	return (
		<CardSlotWrapper ref={setNodeRef}>
			<CardSlotBottom>
				<div>{slotIndex + 1}</div>
				{name && <div>{name}</div>}
			</CardSlotBottom>
			{card && (
				<MovableCard
					key={card.id}
					id={card.id}
					title={card.name}
					rows={card.rows}
					footer={card.footer}
					info={card.info}
					canAnimate={canAnimate}
					variant={isValid ? 'positive' : undefined}
					draggable={!isValid}
				/>
			)}
		</CardSlotWrapper>
	);
};
