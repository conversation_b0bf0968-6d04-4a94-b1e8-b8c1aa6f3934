import { useCallback, useMemo, useState } from 'react';
import { ConnectColumns } from './ConnectColumns';
import { MovableCards } from './MovableCards';
import { TableDrop } from './TableDrop';
import { useAppContext } from '@/contexts/AppContext';
import { useTranslation } from '@platvorm/i18n';
import styled from 'styled-components';
import { GameIntro } from './GameIntro';
import { Header } from './Header';
import { WallGameView } from '@/components/Wall/WallGameView.tsx';
import { useWallContext } from '@/contexts/WallContext.tsx';
import { createWhacAMoleGame } from '@/features/games/WhacAMole/WhacAMole.ts';

const Container = styled.div`
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-between;
	height: 100vh;
	padding: 240px 120px 128px;

	h1 {
		margin: 0;
	}
`;

export const GameView = () => {
	const [gameStarted, setGameStarted] = useState(false);
	const [gameCompleted, setGameCompleted] = useState(false);
	const { currentGame, nextGame } = useAppContext();
	const { t } = useTranslation();
	const { mainWall } = useWallContext();

	const title = useMemo(
		() => currentGame && (gameCompleted ? t(`${currentGame.id}.onCompletedTitle`) : t(`${currentGame.id}.title`)),
		[currentGame, gameCompleted, t],
	);

	const handleCompleted = useCallback(
		(isValid: boolean) => {
			setGameCompleted(isValid);

			const nextGameTimeout = setTimeout(() => {
				if (isValid) {
					setGameStarted(false);
					setGameCompleted(false);
					nextGame();
				}
			}, 2000);

			return () => {
				clearTimeout(nextGameTimeout);
			};
		},
		[nextGame],
	);

	const gameComponent = useMemo(() => {
		if (!currentGame) return null;

		const whacAMole = createWhacAMoleGame(3, mainWall, mainWall);

		switch (currentGame.type) {
			case 'sort':
				return (
					<MovableCards
						cards={(currentGame as SortGame).cards}
						slots={(currentGame as SortGame).slots}
						onCompleted={handleCompleted}
					/>
				);
			case 'connect':
				return (
					<ConnectColumns
						columns={(currentGame as ConnectGame).columns}
						groupingId={(currentGame as ConnectGame).groupingId}
						onCompleted={handleCompleted}
					/>
				);
			case 'table':
				return <TableDrop columns={(currentGame as TableGame).columns} onCompleted={handleCompleted} />;
			case 'mole':
				return <WallGameView game={whacAMole} />;
			default:
				return null;
		}
	}, [currentGame, handleCompleted, mainWall]);

	return (
		<Container>
			<Header />
			{title && <h1 dangerouslySetInnerHTML={{ __html: title }} />}

			{gameComponent}

			{!gameStarted && <GameIntro onStart={() => setGameStarted(true)} />}
		</Container>
	);
};
