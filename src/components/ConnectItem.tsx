import { FC } from 'react';
import styled from 'styled-components';

const Item = styled.div<{ $variant: 'default' | 'positive' | 'negative' | 'active' }>`
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 32px;
	width: 600px;
	padding: 48px;
	text-align: center;
	font-size: 54px;
	font-weight: 500;
	background-color: ${({ $variant }) => ($variant === 'default' ? 'var(--white)' : `var(--${$variant})`)};
	color: ${({ $variant }) => ($variant === 'default' || $variant === 'active' ? 'var(--text)' : 'var(--white)')};
`;

interface ConnectItemProps {
	item: ConnectItem;
	onClick: () => void;
	active?: boolean;
	correct?: boolean;
}

export const ConnectItem: FC<ConnectItemProps> = ({ item, onClick, active, correct }) => {
	return (
		<Item
			onTouchStart={onClick}
			$variant={correct === undefined ? (active ? 'active' : 'default') : correct ? 'positive' : 'negative'}
		>
			{item.name}
		</Item>
	);
};
