import { useDroppable } from '@dnd-kit/core';
import { FC } from 'react';
import styled from 'styled-components';

const DroppableContainer = styled.div<{ $highlighted: boolean }>`
	position: absolute;
	inset: 0;
	width: 100%;
	height: 100%;
	background-color: ${({ $highlighted }) => ($highlighted ? 'var(--active)' : 'rgba(81, 43, 43, 0.08)')};
	border-radius: 8px;
`;

interface TableSlotProps {
	id: string;
}

export const TableSlot: FC<TableSlotProps> = ({ id }) => {
	const { setNodeRef, isOver } = useDroppable({
		id,
	});

	return <DroppableContainer ref={setNodeRef} $highlighted={isOver}></DroppableContainer>;
};
