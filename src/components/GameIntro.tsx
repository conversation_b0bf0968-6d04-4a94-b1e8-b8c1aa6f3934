import { useAppContext } from '@/contexts/AppContext';
import styled from 'styled-components';
import { FC } from 'react';
import { SlideImage, TextButton } from '@swedbank/components';
import { getMediaPath } from '@/utils/assets';

const GameIntroContainer = styled.div`
	position: fixed;
	inset: 0;
	z-index: 2;
	background-color: var(--rose);
`;

const StyledTextButton = styled(TextButton)`
	position: absolute;
	bottom: 344px;
	left: 50%;
	transform: translateX(-50%);
`;

interface GameIntroProps {
	onStart?: () => void;
}

export const GameIntro: FC<GameIntroProps> = ({ onStart }) => {
	const { gameConfig } = useAppContext();

	if (!gameConfig?.slide) return;
	return (
		<GameIntroContainer>
			<SlideImage src={getMediaPath(gameConfig?.slide)} alt="" />
			<StyledTextButton onTouchStart={onStart}>Alusta</StyledTextButton>
		</GameIntroContainer>
	);
};
