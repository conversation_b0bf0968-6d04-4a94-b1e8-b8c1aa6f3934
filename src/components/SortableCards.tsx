import { FC, useCallback, useMemo, useState } from 'react';
import styled from 'styled-components';
import { SortableCard } from './SortableCard';
import { closestCenter, DndContext, TouchSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, horizontalListSortingStrategy, SortableContext } from '@dnd-kit/sortable';
import { TextButton } from '@swedbank/components';

const SortableCardsContainer = styled.div`
	display: flex;
	gap: 64px;
`;

interface SortableCardsProps {
	cards: Card[];
	correctOrder: string[];
	onCompleted?: (isValid: boolean) => void;
}

export const SortableCards: FC<SortableCardsProps> = ({ cards, correctOrder, onCompleted }) => {
	const [sortedCards, setSortedCards] = useState(cards);
	const [answerState, setAnswerState] = useState<boolean | undefined>(undefined);
	const sensors = useSensors(useSensor(TouchSensor));

	const isCorrectOrder = useMemo(() => {
		return sortedCards.map((card) => card.id).join('-') === correctOrder.join('-');
	}, [correctOrder, sortedCards]);

	const handleOrderCheck = useCallback(() => {
		setAnswerState(isCorrectOrder);
		onCompleted?.(isCorrectOrder);
	}, [isCorrectOrder, onCompleted]);

	const handleDragEnd = useCallback(
		(event: any) => {
			const { active, over } = event;

			if (active.id !== over.id) {
				setSortedCards((items) => {
					const oldIndex = items.findIndex((item) => item.id === active.id);
					const newIndex = items.findIndex((item) => item.id === over.id);

					return arrayMove(items, oldIndex, newIndex);
				});
			}
		},
		[setSortedCards],
	);

	return (
		<>
			<SortableCardsContainer>
				<DndContext
					sensors={sensors}
					collisionDetection={closestCenter}
					onDragEnd={handleDragEnd}
					onDragStart={() => setAnswerState(undefined)}
				>
					<SortableContext items={sortedCards} strategy={horizontalListSortingStrategy}>
						{sortedCards?.map((card) => (
							<SortableCard
								id={card.id}
								title={card.name}
								rows={card.rows}
								footer={card.footer}
								key={card.id}
								variant={answerState !== undefined ? (answerState === true ? 'positive' : 'negative') : undefined}
							/>
						))}
					</SortableContext>
				</DndContext>
			</SortableCardsContainer>
			<TextButton onClick={handleOrderCheck} style={{ marginTop: '20px' }} variant="primary">
				Kontrolli
			</TextButton>
		</>
	);
};
