import styled from 'styled-components';
import { WallState } from '@/features/wall/WallState.ts';
import { WallButton } from '@/features/wall/WallButton.ts';
import { useToggle } from '@/hooks/useToggle.ts';
import { useKeyEvents } from '@/hooks/useKeyEvents.ts';

interface GridProps {
	$width: number;
	$height: number;
}

const Grid = styled.div<GridProps>`
	display: grid;
	grid-template-columns: repeat(${(p) => p.$width}, 1fr);
	grid-template-rows: repeat(${(p) => p.$height}, 1fr);
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: black;
	z-index: 1000;
`;

const Cell = styled.div`
	border: 1px solid white;
	color: cadetblue;
	font-size: 24px;
	display: flex;
	justify-content: center;
	align-items: center;
`;

interface WallDebugProps {
	wallState: WallState;
}

interface ButtonCellProps {
	buttonState: WallButton;
}

function ButtonCell({ buttonState }: ButtonCellProps) {
	const onPointerDown = () => (buttonState.pressed = true);
	const onPointerUp = () => (buttonState.pressed = false);

	const style = {
		backgroundColor: `rgba(255, 255, 255, ${buttonState.brightness})`,
	};

	return (
		<Cell onPointerDown={onPointerDown} onPointerUp={onPointerUp} style={style}>
			{buttonState.pressed ? '1' : '0'}
		</Cell>
	);
}

export function WallDebug({ wallState }: WallDebugProps) {
	const { value: visible, toggle } = useToggle(true);

	useKeyEvents({
		w: toggle,
	});

	if (!visible) return null;

	return (
		<Grid $width={wallState.width} $height={wallState.height}>
			{wallState.buttons.map((button) => (
				<ButtonCell key={button.id} buttonState={button} />
			))}
		</Grid>
	);
}
