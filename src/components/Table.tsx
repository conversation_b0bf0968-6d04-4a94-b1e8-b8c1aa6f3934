import { FC } from 'react';
import styled from 'styled-components';
import { TableSlot } from './TableSlot';
import { useTranslation } from '@platvorm/i18n';

const TableContainer = styled.div`
	display: grid;
	grid-template-columns: auto 470px 470px;
	gap: 12px;
	padding: 40px 48px;
	width: 100%;
	background-color: var(--white);
	border-radius: 32px;
`;

const TableColumn = styled.div`
	display: flex;
	flex-direction: column;
	gap: 20px;
`;

const ColumnHeader = styled.div`
	font-family: 'Swedbank Headline', sans-serif;
	font-weight: 900;
	font-size: 68px;
	line-height: 1;
	height: 110px;
	text-align: center;
	margin-top: 10px;
`;

const ColumnFooter = styled.div`
	font-size: 24px;
	text-align: center;
`;

interface TableItemProps {
	$alignment: 'left' | 'center';
	$border: boolean;
	$size: 'large' | 'medium';
}

const TableItem = styled.div<TableItemProps>`
	position: relative;
	display: flex;
	justify-content: ${({ $alignment }) => ($alignment === 'left' ? 'flex-start' : 'center')};
	align-items: center;
	height: 105px;
	border: 1px solid var(--border);
	font-size: ${({ $size }) => ($size === 'large' ? '54px' : '44px')};
	padding: 20px;
	border-bottom: ${({ $border }) => ($border ? '2px solid var(--border-color)' : 'none')};
`;

interface TableProps {
	columns: TableColumn[];
}

export const Table: FC<TableProps> = ({ columns }) => {
	const { t } = useTranslation();
	return (
		<TableContainer>
			{columns.map((column, index) => (
				<TableColumn key={index}>
					<ColumnHeader>{column.name || ''}</ColumnHeader>
					{column.items.map((item) => (
						<TableItem
							key={item.id}
							$alignment={index === 0 ? 'left' : 'center'}
							$border={!item.droppable}
							$size={index === 0 ? 'large' : 'medium'}
						>
							{item.droppable ? (
								<TableSlot id={item.id} />
							) : (
								<div dangerouslySetInnerHTML={{ __html: t(`${item.id}.name`) }} />
							)}
						</TableItem>
					))}
					{column.footer && <ColumnFooter dangerouslySetInnerHTML={{ __html: t(column.footer) }} />}
				</TableColumn>
			))}
		</TableContainer>
	);
};
