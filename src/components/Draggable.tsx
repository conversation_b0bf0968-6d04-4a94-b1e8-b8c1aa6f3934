import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { FC, PropsWithChildren } from 'react';
import styled from 'styled-components';

export const Item = styled.div<{ $visible?: boolean; $size?: 'large' | 'medium'; $height?: number }>`
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: ${({ $size }) => ($size === 'medium' ? '44px' : '54px')};
	height: ${({ $height }) => ($height ? `${$height}px` : 'auto')};
	background-color: var(--white);
	padding: 20px 24px;
	border-radius: 32px;
	box-shadow:
		0px 0px 4px 0px rgba(81, 43, 43, 0.2),
		0px 5.5px 30px 0px rgba(81, 43, 43, 0.1);

	p {
		display: flex;
		align-items: center;
		gap: 10px;
		line-height: 1;
		margin: 0;
	}
`;

interface DraggableProps {
	id: string;
	content: string;
	size?: 'large' | 'medium';
	height?: number;
}

export const Draggable: FC<PropsWithChildren<DraggableProps>> = ({ id, content, size, height }) => {
	const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({ id });
	const style: React.CSSProperties = {
		transform: CSS.Translate.toString(transform),
		position: isDragging ? 'relative' : 'inherit',
		zIndex: isDragging ? 3 : 0,
		gridRow: 1,
	};
	return (
		<div ref={setNodeRef} style={style} {...attributes} {...listeners}>
			<Item dangerouslySetInnerHTML={{ __html: content }} $size={size} $height={height} />
		</div>
	);
};
