import {
	arrow,
	autoUpdate,
	flip,
	FloatingArrow,
	FloatingFocusManager,
	offset,
	shift,
	useClick,
	useDismiss,
	useFloating,
	useInteractions,
	useRole,
} from '@floating-ui/react';
import { FC, PropsWithChildren, useRef, useState } from 'react';
import styled from 'styled-components';
import TooltipIcon from '@/assets/tooltip-icon.svg?react';
import { createPortal } from 'react-dom';

const PopoverButton = styled.button`
	position: absolute;
	top: 56px;
	right: 40px;
	background-color: transparent;
	border: none;
	padding: 0;
`;

const PopoverContent = styled.div`
	background-color: var(--white);
	border-radius: 16px;
	padding: 40px;
	box-shadow:
		0px 0px 4px 0px rgba(81, 43, 43, 0.2),
		0px 5.5px 30px var(--Number, 24px) rgba(81, 43, 43, 0.1);
	width: 777px;
	font-size: 44px;
	color: var(--text);
`;

export const InfoPopover: FC<PropsWithChildren> = ({ children }) => {
	const [isOpen, setIsOpen] = useState(false);

	const arrowRef = useRef(null);
	const { refs, floatingStyles, context } = useFloating({
		open: isOpen,
		onOpenChange: setIsOpen,
		middleware: [
			offset(45),
			flip(),
			shift(),
			arrow({
				element: arrowRef,
			}),
		],
		whileElementsMounted: autoUpdate,
		placement: 'top',
	});

	const click = useClick(context);
	const dismiss = useDismiss(context);
	const role = useRole(context);

	const { getReferenceProps, getFloatingProps } = useInteractions([click, dismiss, role]);

	return (
		<div>
			<PopoverButton ref={refs.setReference} {...getReferenceProps()}>
				<TooltipIcon />
			</PopoverButton>

			{isOpen &&
				createPortal(
					<FloatingFocusManager context={context} modal={false}>
						<div ref={refs.setFloating} style={floatingStyles} {...getFloatingProps()}>
							<PopoverContent>
								<div dangerouslySetInnerHTML={{ __html: children }} />
							</PopoverContent>
							<FloatingArrow
								ref={arrowRef}
								context={context}
								fill="white"
								width={68}
								height={34}
								style={{ transform: 'translateY(-1px)' }}
							/>
						</div>
					</FloatingFocusManager>,
					document.body,
				)}
		</div>
	);
};
