import { FC, useCallback, useEffect, useState } from 'react';
import styled from 'styled-components';
import useTimeout from '@/hooks/useTimeout';
import { ConnectItem } from './ConnectItem';

interface ConnectColumnsProps {
	columns: ConnectColumn[];
	groupingId: string;
	onCompleted: (isValid: boolean) => void;
}

const Columns = styled.div`
	flex: 1;
	display: flex;
	flex-direction: row;
	gap: 144px;
	margin-top: 120px;
`;

const Column = styled.div`
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 24px;

	h2 {
		margin: 0;
	}
`;

const ConnectColumn = styled.div`
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 32px;
`;

type Connection = Record<string, ConnectItem>;

export const ConnectColumns: FC<ConnectColumnsProps> = ({ columns, groupingId, onCompleted }) => {
	const [activeConnection, setActiveConnection] = useState<Connection>();
	const [submittedConnection, setSubmittedConnection] = useState<Connection>();
	const [correctConnections, setCorrectConnections] = useState<Connection[]>([]);

	const { reset: setSubmitTimeout, clear: clearSubmitTimeout } = useTimeout(() => {
		setSubmittedConnection(undefined);
		setActiveConnection(undefined);
	}, 2000);

	const checkSubmittedConnection = useCallback(
		(connection: Connection) => {
			const group = connection[groupingId]?.groups[0];
			return Object.values(connection).every((item) => item.groups.includes(group));
		},
		[groupingId],
	);

	const submitConnection = useCallback(
		(connection: Connection) => {
			setSubmittedConnection(connection);
			setSubmitTimeout();

			if (checkSubmittedConnection(connection)) {
				setCorrectConnections((prev) => [...prev, connection]);
			}
		},
		[setSubmitTimeout, checkSubmittedConnection],
	);

	const handleConnectItemClick = useCallback(
		(columnId: string, item: ConnectItem) => {
			if (submittedConnection) {
				clearSubmitTimeout();
				setSubmittedConnection(undefined);
				setActiveConnection({ [columnId]: item });
				return;
			}

			const newConnection = { ...activeConnection, [columnId]: item };
			setActiveConnection(newConnection);

			const allConnected = Object.keys(newConnection).length === columns.length;

			if (allConnected) {
				submitConnection(newConnection);
			}
		},
		[submittedConnection, activeConnection, columns.length, clearSubmitTimeout, submitConnection],
	);

	useEffect(() => {
		if (correctConnections.length === columns[0]?.items.length) {
			onCompleted(true);
		}
	}, [columns, correctConnections, onCompleted]);

	const isItemCorrect = (columnId: string, itemId: string) =>
		correctConnections.some((conn) => conn[columnId]?.id === itemId);

	return (
		<Columns>
			{columns.map((column) => (
				<Column key={column.id}>
					<h2>{column.name}</h2>
					<ConnectColumn>
						{column.items.map((item) => {
							const active = activeConnection?.[column.id]?.id === item.id;
							const correct =
								isItemCorrect(column.id, item.id) ||
								(submittedConnection?.[column.id]?.id === item.id ? isItemCorrect(column.id, item.id) : undefined);

							return (
								<ConnectItem
									key={item.id}
									item={item}
									onClick={() => handleConnectItemClick(column.id, item)}
									active={active}
									correct={correct}
								/>
							);
						})}
					</ConnectColumn>
				</Column>
			))}
		</Columns>
	);
};
