import styled from 'styled-components';
import { Timer } from '@swedbank/components';
import { useAppContext } from '@/contexts/AppContext';

const HeaderContainer = styled.div`
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	z-index: 3;
	font-family: 'Swedbank Headline', sans-serif;
	font-weight: 900;
	font-size: 72px;
	line-height: 1;
	padding: 60px 60px 130px 60px;
`;

const TopBar = styled.div`
	display: flex;
	justify-content: end;
	margin-bottom: 32px;
`;

export const Header = () => {
	const { totalGameTimeLeft } = useAppContext();

	return (
		<HeaderContainer>
			<TopBar>
				<Timer timeLeft={totalGameTimeLeft} />
			</TopBar>
		</HeaderContainer>
	);
};
