import { FC, useCallback, useMemo, useState } from 'react';
import { Table } from './Table';
import { Draggable, Item } from './Draggable';
import {
	DndContext,
	DragEndEvent,
	DragOverlay,
	DragStartEvent,
	TouchSensor,
	useSensor,
	useSensors,
} from '@dnd-kit/core';
import { restrictToWindowEdges } from '@dnd-kit/modifiers';
import styled from 'styled-components';
import { createPortal } from 'react-dom';
import { useTranslation } from '@platvorm/i18n';

const GameContainer = styled.div`
	display: grid;
	grid-template-columns: 828px 1800px 828px;
	align-items: center;
	gap: 96px;
	margin-bottom: 40px;
`;

const Terms = styled.div`
	display: flex;
	flex-wrap: wrap;
	gap: 48px;
	justify-content: end;
`;

const Definitions = styled.div`
	display: flex;
	flex-wrap: wrap;
	gap: 48px;
	justify-content: start;
`;

const PlaceholderItem = styled(Item)`
	opacity: 0;
	pointer-events: none;
	height: 105px;
`;

interface TableDropProps {
	columns: TableColumn[];
	onCompleted?: (isValid: boolean) => void;
}

export const TableDrop: FC<TableDropProps> = ({ columns, onCompleted }) => {
	const { t } = useTranslation();
	const [activeItem, setActiveItem] = useState<TableItem | null>(null);
	const [currentColumns, setCurrentColumns] = useState<TableColumn[]>(columns);

	const sensors = useSensors(useSensor(TouchSensor, { activationConstraint: { distance: 10 } }));

	const initialTermsShuffled = useMemo(() => {
		const initialTerms = columns[0].items.filter((item) => item.droppable);
		shuffle(initialTerms);
		return initialTerms;
	}, [columns]);

	const initialDefinitionsShuffled = useMemo(() => {
		const initialDefinitions = [columns[1], columns[2]].flatMap((column) =>
			column.items.filter((item) => item.droppable),
		);
		shuffle(initialDefinitions);
		return initialDefinitions;
	}, [columns]);

	const terms = useMemo(() => currentColumns[0].items.filter((item) => item.droppable), [currentColumns]);
	const definitions = useMemo(
		() => [currentColumns[1], currentColumns[2]].flatMap((column) => column.items.filter((item) => item.droppable)),
		[currentColumns],
	);

	const handleDragStart = useCallback(
		(event: DragStartEvent) => {
			const { active } = event;
			setActiveItem(currentColumns.flatMap((column) => column.items).find((item) => item.id === active.id) || null);
			const newColumns = currentColumns.map((column) => {
				return {
					...column,
					items: column.items.map((item) => {
						if (item.id === active.id) {
							return { ...item, droppable: true };
						}
						return item;
					}),
				};
			});
			setCurrentColumns(newColumns);
		},
		[currentColumns],
	);

	const handleDragEnd = useCallback(
		(event: DragEndEvent) => {
			const { active, over } = event;
			setActiveItem(null);
			if (active.id === over?.id) {
				const newColumns = currentColumns.map((column) => {
					return {
						...column,
						items: column.items.map((item) => {
							if (item.id === active.id) {
								return { ...item, droppable: false };
							}
							if (item.id === over?.id) {
								return { ...item, droppable: true };
							}
							return item;
						}),
					};
				});
				setCurrentColumns(newColumns);
				if (onCompleted) {
					const isValid = newColumns.every((column) => column.items.every((item) => !item.droppable));
					onCompleted(isValid);
				}
			}
		},
		[currentColumns, onCompleted],
	);

	return (
		<DndContext
			sensors={sensors}
			modifiers={[restrictToWindowEdges]}
			onDragStart={handleDragStart}
			onDragEnd={handleDragEnd}
			onDragCancel={() => setActiveItem(null)}
		>
			<GameContainer>
				<Terms>
					{initialTermsShuffled.map((initialTerm) =>
						terms.find((term) => term.id === initialTerm.id) ? (
							<Draggable id={initialTerm.id} key={initialTerm.id} content={t(`${initialTerm.id}.name`)} height={105} />
						) : (
							<PlaceholderItem dangerouslySetInnerHTML={{ __html: t(`${initialTerm.id}.name`) }} />
						),
					)}
				</Terms>
				<Table columns={currentColumns} />
				<Definitions>
					{initialDefinitionsShuffled.map((initialDefinition) =>
						definitions.find((definition) => definition.id === initialDefinition.id) ? (
							<Draggable
								id={initialDefinition.id}
								key={initialDefinition.id}
								content={t(`${initialDefinition.id}.name`)}
								size="medium"
								height={105}
							/>
						) : (
							<PlaceholderItem dangerouslySetInnerHTML={{ __html: t(`${initialDefinition.id}.name`) }} $size="medium" />
						),
					)}
				</Definitions>
			</GameContainer>
			{createPortal(
				<DragOverlay>
					{activeItem ? (
						<Item
							id={activeItem.id}
							dangerouslySetInnerHTML={{ __html: t(`${activeItem.id}.name`) }}
							$size={activeItem.size || 'large'}
							$height={105}
						></Item>
					) : null}
				</DragOverlay>,
				document.body,
			)}
		</DndContext>
	);
};

function shuffle(array: unknown[]) {
	let currentIndex = array.length;

	while (currentIndex != 0) {
		const randomIndex = Math.floor(Math.random() * currentIndex);
		currentIndex--;

		[array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]];
	}
}
