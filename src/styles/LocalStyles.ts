import { createGlobalStyle } from 'styled-components';

export const LocalStyles = createGlobalStyle`
    :root {
        --positive-transparent: rgba(0, 178, 50, 0.3);
        --positive: #51971B;
        --negative: #C5131C;
        --bark-extra-soft: #e5dcd7;

        --border-color: var(--bark-extra-soft);
    }

    body {
        margin: 0;
        padding: 0;
        overflow: hidden;
        background-color: var(--rose);
    }

    h1 {
        font-size: 120px;
        text-align: center;
    }

    h3 {
        font-family: "Swedbank Headline", sans-serif;
        font-weight: 900;
        line-height: 1;
        font-size: 68px;
        margin: 0 0 32px 0;
    }

    strong {
        display: inline-block;
        font-size: 60px;
        font-family: 'Swedbank Headline', sans-serif;
        font-weight: 900;
        color: var(--swedbank-orange-text);
        margin-top: 8px;
    }

    em {
        font-size: 44px;
        color: #7B6362;
        font-style: normal;
    }

    * {
        box-sizing: border-box;
    }
`;
