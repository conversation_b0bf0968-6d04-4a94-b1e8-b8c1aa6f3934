import { useCallback, useEffect } from 'react';

type KeyEvents = { [key: string]: () => void };

export function useKeyEvents(events: KeyEvents, enabled = true) {
	const handleKeyDown = useCallback(
		(event: KeyboardEvent) => {
			if (!enabled) return;
			const eventFunction = events[event.key] || events['any'];
			if (eventFunction) {
				eventFunction();
			}
		},
		[enabled, events],
	);

	useEffect(() => {
		document.addEventListener('keydown', handleKeyDown);

		return () => {
			document.removeEventListener('keydown', handleKeyDown);
		};
	}, [handleKeyDown]);
}
