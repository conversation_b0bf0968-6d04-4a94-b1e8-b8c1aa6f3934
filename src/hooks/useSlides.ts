import { useCallback, useState } from 'react';

interface useSlidesProps {
	slides?: string[];
	onSlidesFinished?: () => void;
}

export function useSlides({ slides, onSlidesFinished }: useSlidesProps) {
	const [slideIndex, setSlideIndex] = useState(0);

	const nextSlide = useCallback(() => {
		if (!slides || slides.length === 0) {
			return;
		}
		if (slideIndex < slides.length - 1) {
			setSlideIndex((prev) => prev + 1);
		} else {
			onSlidesFinished?.();
		}
	}, [slides, slideIndex, onSlidesFinished]);

	const previousSlide = useCallback(() => {
		if (slideIndex > 0) {
			setSlideIndex((prev) => prev - 1);
		}
	}, [slideIndex]);

	const restartSlides = useCallback(() => {
		setSlideIndex(0);
	}, []);

	return {
		slideIndex,
		currentSlide: slides?.[slideIndex],
		totalSlides: slides?.length || 0,
		nextSlide,
		previousSlide,
		restartSlides,
	};
}
