import { useEffect, useRef } from 'react';
import { ElectronSerial } from '@/utils/ElectronSerial.ts';

export const useSerialPortConnection = () => {
	const serial = useRef<ElectronSerial | null>(null);

	useEffect(() => {
		if (serial.current) return; // Already have a connection

		ElectronSerial.getConnection(
			{ vendorId: '10C4', productId: 'EA60' },
			115200
		).then(connection => {
			if (connection) {
				console.log('[useSerialPortConnection] Got serial connection');
				serial.current = connection;
			} else {
				console.log('[useSerialPortConnection] No matching serial port found');
			}
		});
	}, []);

	return serial.current;
};