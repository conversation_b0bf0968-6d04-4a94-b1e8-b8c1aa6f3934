import { useEffect, useRef } from 'react';
import { ElectronSerial } from '@/utils/ElectronSerial.ts';

export const useSerialPortConnection = () => {
	const serial = useRef<ElectronSerial | null>(null);

	useEffect(() => {
		ElectronSerial.listPorts().then(ports => {
			console.log('SerialPorts:', ports);
			if (serial.current || !ports || ports.length === 0) return;

			const port = ports.find(p => p.vendorId === '10C4' && p.productId === 'EA60');
			if (!port) return;

			serial.current = new ElectronSerial(port.path, 115200);
		});
	}, [serial]);

	return serial.current;
};