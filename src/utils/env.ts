
const truthyValues = ['true', '1', 'yes', 'on'];

export class Env {
	static get(key: string): string {
		const returnVal = window.api?.env?.[key] || import.meta.env[key];

		if (returnVal === undefined) {
			throw new Error(`Environment variable ${key} not found`);
		}

		return returnVal;
	}

	static getNumber(key: string): number {
		return Number(this.get(key));
	}

	static getBoolean(key: string): boolean {
		return truthyValues.includes(this.get(key).toLowerCase());
	}
}
