import { debounce } from 'es-toolkit';

const decoder = new TextDecoder('utf-8');

export interface PortInfo {
	friendlyName: string;
	locationId: string;
	manufacturer: string;
	path: string;
	pnpId: string;
	productId: string;
	serialNumber: string;
	vendorId: string;
}

export interface SerialConnectionInfo {
	serialNumber?: string;
	path?: string;
	vendorId?: string;
	productId?: string;
}

/**
 * Usage examples:
 *
 * // Connect by serial number
 * const connection = await ElectronSerial.getConnection({ serialNumber: 'ABC123' }, 115200);
 *
 * // Connect by vendor/product ID
 * const connection = await ElectronSerial.getConnection({ vendorId: '10C4', productId: 'EA60' }, 115200);
 *
 * // Connect by path
 * const connection = await ElectronSerial.getConnection({ path: '/dev/ttyUSB0' }, 115200);
 */

export class ElectronSerial {
	private readonly serialId: number = -1;
	private static connectionPool: Map<string, ElectronSerial> = new Map();

	static async listPorts(): Promise<PortInfo[] | undefined> {
		try {
			if (!window.serialApi) {
				console.error('[ElectronSerial] serialApi not available');
				return undefined;
			}
			const ports = await window.serialApi.listPorts();
			console.log('[ElectronSerial] Listed ports:', ports);
			return ports;
		} catch (error) {
			console.error('[ElectronSerial] Error listing ports:', error);
			return undefined;
		}
	}

	/**
	 * Get or create a serial connection based on connection info and baud rate.
	 * This method ensures that only one connection per unique identifier is created.
	 */
	static async getConnection(connectionInfo: SerialConnectionInfo, baudRate: number): Promise<ElectronSerial | null> {
		try {
			const ports = await this.listPorts();
			if (!ports) {
				console.error('[ElectronSerial] No ports available');
				return null;
			}

			// Find the matching port
			const port = ports.find(p => {
				if (connectionInfo.serialNumber && p.serialNumber === connectionInfo.serialNumber) return true;
				if (connectionInfo.path && p.path === connectionInfo.path) return true;
				if (connectionInfo.vendorId && connectionInfo.productId &&
					p.vendorId === connectionInfo.vendorId && p.productId === connectionInfo.productId) return true;
				return false;
			});

			if (!port) {
				console.error('[ElectronSerial] Port not found for connection info:', connectionInfo);
				return null;
			}

			// Create a unique key for this connection
			const connectionKey = `${port.path}:${baudRate}`;

			// Check if connection already exists
			if (this.connectionPool.has(connectionKey)) {
				console.log('[ElectronSerial] Reusing existing connection:', connectionKey);
				return this.connectionPool.get(connectionKey)!;
			}

			// Create new connection
			console.log('[ElectronSerial] Creating new connection:', connectionKey);
			const connection = new ElectronSerial(port.path, baudRate);
			if (connection.isValid()) {
				this.connectionPool.set(connectionKey, connection);
				return connection;
			}

			return null;
		} catch (error) {
			console.error('[ElectronSerial] Error getting connection:', error);
			return null;
		}
	}

	private constructor(path: string, baudRate: number) {
		try {
			if (!window.serialApi) {
				console.error('[ElectronSerial] serialApi not available in constructor');
				return;
			}
			const serial = window.serialApi.openPort(path, baudRate);
			console.log('[ElectronSerial] Opened port, serialId:', serial);
			if (serial === undefined) {
				console.error('[ElectronSerial] Failed to open port');
				return;
			}
			// Use object.defineProperty to modify readonly property
			Object.defineProperty(this, 'serialId', { value: serial, writable: false });
		} catch (error) {
			console.error('[ElectronSerial] Error opening port:', error);
		}
	}

	/**
	 * Check if this serial connection is valid (successfully opened)
	 */
	isValid(): boolean {
		return this.serialId !== -1;
	}

	onData = (callback: (data: string) => void) => {
		try {
			console.log('[ElectronSerial] Setting up onData for serialId:', this.serialId);
			if (!window.serialApi) {
				console.error('[ElectronSerial] serialApi not available for onData');
				return;
			}
			if (this.serialId === -1) {
				console.error('[ElectronSerial] Invalid serialId for onData');
				return;
			}
			return window.serialApi.onData(
				this.serialId,
				debounce((d) => {
					const dataString = decoder.decode(d).trim();
					console.log('[ElectronSerial] Received data:', dataString);
					callback(dataString);
				}, 10),
			);
		} catch (error) {
			console.error('[ElectronSerial] Error setting up onData:', error);
		}
	}

	write(data: string) {
		try {
			if (!window.serialApi) {
				console.error('[ElectronSerial] serialApi not available for write');
				return;
			}
			if (this.serialId === -1) {
				console.error('[ElectronSerial] Invalid serialId for write');
				return;
			}
			console.log('[ElectronSerial] Writing data:', data);
			window.serialApi.write(this.serialId, data);
		} catch (error) {
			console.error('[ElectronSerial] Error writing data:', error);
		}
	}

	writeLine(data: string) {
		console.log('[ElectronSerial] Writing line:', data);
		this.write(data + '\n');
	}

	close() {
		try {
			if (!window.serialApi) {
				console.error('[ElectronSerial] serialApi not available for close');
				return;
			}
			if (this.serialId === -1) {
				console.error('[ElectronSerial] Invalid serialId for close');
				return;
			}
			console.log('[ElectronSerial] Closing port with serialId:', this.serialId);
			window.serialApi.close(this.serialId);

			// Remove from connection pool
			for (const [key, connection] of ElectronSerial.connectionPool.entries()) {
				if (connection === this) {
					ElectronSerial.connectionPool.delete(key);
					break;
				}
			}
		} catch (error) {
			console.error('[ElectronSerial] Error closing port:', error);
		}
	}

	/**
	 * Close all connections in the pool. Useful for cleanup.
	 */
	static closeAllConnections() {
		console.log('[ElectronSerial] Closing all connections');
		for (const connection of this.connectionPool.values()) {
			connection.close();
		}
		this.connectionPool.clear();
	}
}
