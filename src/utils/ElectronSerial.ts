import { debounce } from 'es-toolkit';

const decoder = new TextDecoder('utf-8');

export interface PortInfo {
	friendlyName: string;
	locationId: string;
	manufacturer: string;
	path: string;
	pnpId: string;
	productId: string;
	serialNumber: string;
	vendorId: string;
}

export class ElectronSerial {
	private readonly serialId: number = -1;

	static async listPorts(): Promise<PortInfo[] | undefined> {
		try {
			if (!window.serialApi) {
				console.error('[ElectronSerial] serialApi not available');
				return undefined;
			}
			const ports = await window.serialApi.listPorts();
			console.log('[ElectronSerial] Listed ports:', ports);
			return ports;
		} catch (error) {
			console.error('[ElectronSerial] Error listing ports:', error);
			return undefined;
		}
	}

	constructor(path: string, baudRate: number) {
		try {
			if (!window.serialApi) {
				console.error('[ElectronSerial] serialApi not available in constructor');
				return;
			}
			const serial = window.serialApi.openPort(path, baudRate);
			console.log('[ElectronSerial] Opened port, serialId:', serial);
			if (serial === undefined) {
				console.error('[ElectronSerial] Failed to open port');
				return;
			}
			this.serialId = serial;
		} catch (error) {
			console.error('[ElectronSerial] Error opening port:', error);
		}
	}

	onData = (callback: (data: string) => void) => {
		try {
			console.log('[ElectronSerial] Setting up onData for serialId:', this.serialId);
			if (!window.serialApi) {
				console.error('[ElectronSerial] serialApi not available for onData');
				return;
			}
			if (this.serialId === -1) {
				console.error('[ElectronSerial] Invalid serialId for onData');
				return;
			}
			return window.serialApi.onData(
				this.serialId,
				debounce((d) => {
					const dataString = decoder.decode(d).trim();
					console.log('[ElectronSerial] Received data:', dataString);
					callback(dataString);
				}, 10),
			);
		} catch (error) {
			console.error('[ElectronSerial] Error setting up onData:', error);
		}
	}

	write(data: string) {
		try {
			if (!window.serialApi) {
				console.error('[ElectronSerial] serialApi not available for write');
				return;
			}
			if (this.serialId === -1) {
				console.error('[ElectronSerial] Invalid serialId for write');
				return;
			}
			console.log('[ElectronSerial] Writing data:', data);
			window.serialApi.write(this.serialId, data);
		} catch (error) {
			console.error('[ElectronSerial] Error writing data:', error);
		}
	}

	writeLine(data: string) {
		console.log('[ElectronSerial] Writing line:', data);
		this.write(data + '\n');
	}

	close() {
		try {
			if (!window.serialApi) {
				console.error('[ElectronSerial] serialApi not available for close');
				return;
			}
			if (this.serialId === -1) {
				console.error('[ElectronSerial] Invalid serialId for close');
				return;
			}
			console.log('[ElectronSerial] Closing port with serialId:', this.serialId);
			window.serialApi.close(this.serialId);
		} catch (error) {
			console.error('[ElectronSerial] Error closing port:', error);
		}
	}
}
