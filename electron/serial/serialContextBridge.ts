import { SerialPort, DelimiterParser } from 'serialport';
import { contextBridge } from 'electron';

// Add logging to debug issues in packaged app
const log = (message: string, ...args: any[]) => {
	console.log(`[SerialAPI] ${message}`, ...args);
};

class PortWithParser {
	port: SerialPort;
	parser: DelimiterParser;

	constructor(port: SerialPort) {
		this.port = port;
		this.parser = port.pipe(new DelimiterParser({ delimiter: '\n', includeDelimiter: false }));
	}
}

const serialPorts: PortWithParser[] = [];

function getPortById(portId: number): PortWithParser | undefined {
	return serialPorts[portId];
}

export const serialApi = {
	listPorts: async () => {
		try {
			log('Listing serial ports...');
			const ports = await SerialPort.list();
			log('Found ports:', ports);
			return ports;
		} catch (error) {
			log('Error listing ports:', error);
			throw error;
		}
	},

	openPort: (path: string, baudRate: number) => {
		try {
			log(`Opening port ${path} at ${baudRate} baud...`);
			const port = new SerialPort({ path, baudRate });
			const portWithParser = new PortWithParser(port);
			serialPorts.push(portWithParser);
			const portId = serialPorts.length - 1;
			log(`Port opened successfully with ID: ${portId}`);
			return portId;
		} catch (error) {
			log('Error opening port:', error);
			throw error;
		}
	},

	write: (portId: number, data: string) => {
		try {
			// log(`Writing to port ${portId}:`, data);
			const portWithParser = getPortById(portId);
			if (!portWithParser) {
				log(`Port ${portId} not found`);
				return;
			}
			portWithParser.port.write(data);
		} catch (error) {
			log('Error writing to port:', error);
		}
	},

	onData: (portId: number, callback: (data: string) => void) => {
		try {
			log(`Setting up data listener for port ${portId}`);
			const portWithParser = getPortById(portId);
			if (!portWithParser) {
				log(`Port ${portId} not found`);
				return;
			}
			portWithParser.parser.on('data', callback);
		} catch (error) {
			log('Error setting up data listener:', error);
		}
	},

	close: (portId: number) => {
		try {
			log(`Closing port ${portId}`);
			const portWithParser = getPortById(portId);
			if (!portWithParser) {
				log(`Port ${portId} not found`);
				return;
			}
			portWithParser.port.close();
		} catch (error) {
			log('Error closing port:', error);
		}
	},
};

contextBridge.exposeInMainWorld('serialApi', serialApi);
