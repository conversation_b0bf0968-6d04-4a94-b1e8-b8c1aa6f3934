import { contextBridge } from 'electron';
import './serial/serialContextBridge';

const APP_ENV_PREFIX = 'APP_';
const envKeys = Object.keys(process.env);

const appEnvs = envKeys.reduce<Record<string, string>>((acc, key) => {
	if (!key.startsWith(APP_ENV_PREFIX)) return acc;
	return { ...acc, [key]: process.env[key] as string };
}, {});

export const contextApi = {
	node: () => process.versions.node,
	chrome: () => process.versions.chrome,
	electron: () => process.versions.electron,
	assetDir: !process.env.ELECTRON_DEV ? process.env.ASSET_DIR : undefined,
	mediaDir: !process.env.ELECTRON_DEV ? process.env.MEDIA_DIR : undefined,
	env: appEnvs,
};

contextBridge.exposeInMainWorld('api', contextApi);
